
import secrets
import time
from stem import Signal
from stem.control import Controller
from tenacity import retry

@retry
def switch_proxy():
    """切换默认端口Tor身份 - 修复版"""
    with Controller.from_port() as controller:
        controller.authenticate()

        # 关键修复1: 关闭现有电路
        circuits = controller.get_circuits()
        for circuit in circuits:
            if circuit.status == 'BUILT':
                try:
                    controller.close_circuit(circuit.id)
                except:
                    pass

        # 关键修复2: 发送NEWNYM信号
        controller.signal(Signal.NEWNYM)

        # 关键修复3: 必须等待推荐时间
        wait_time = controller.get_newnym_wait()
        time.sleep(wait_time)

        # 关键修复4: 额外等待确保电路建立
        time.sleep(5)

@retry
def switch_tor():
    """切换9051端口Tor身份 - 修复版"""
    with Controller.from_port(port=9051) as controller:
        controller.authenticate()

        # 关键修复1: 关闭现有电路
        circuits = controller.get_circuits()
        for circuit in circuits:
            if circuit.status == 'BUILT':
                try:
                    controller.close_circuit(circuit.id)
                except:
                    pass

        # 关键修复2: 发送NEWNYM信号
        controller.signal(Signal.NEWNYM)

        # 关键修复3: 必须等待推荐时间
        wait_time = controller.get_newnym_wait()
        time.sleep(wait_time)

        # 关键修复4: 额外等待确保电路建立
        time.sleep(5)

# 执行切换
secrets.choice([switch_proxy(), switch_tor()])