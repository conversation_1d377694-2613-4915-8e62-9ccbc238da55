' 函数：TerminateProcess
' 描述：根据进程名终止进程，添加基本错误处理
' 参数：processName - 要结束的进程的名称
Function TerminateProcess(processName)
    On Error Resume Next

    ' 定义本地计算机
    strComputer = "."

    ' 获取 WMI 服务对象
    Set objWMIService = GetObject("winmgmts:\\" & strComputer & "\root\cimv2")
    If Err.Number <> 0 Then
        ' 备用连接方式
        Set objWMIService = GetObject("winmgmts:{impersonationLevel=impersonate}!\\.\root\cimv2")
    End If

    ' 查询指定名称的进程
    Set colProcessList = objWMIService.ExecQuery("SELECT * FROM Win32_Process WHERE Name='" & processName & "'")

    ' 遍历匹配的进程并结束它们
    For Each objProcess in colProcessList
        objProcess.Terminate()
    Next

    On Error GoTo 0
End Function

' 要终止的进程列表
Dim processNames
processNames = Array("v2rayN.exe", "karing.exe")

' 批量终止进程
Dim i
For i = 0 To UBound(processNames)
    TerminateProcess processNames(i)
Next